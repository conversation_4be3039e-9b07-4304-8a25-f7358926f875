datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

generator client {
  provider = "prisma-client-js"
}

enum Role {
  ADMIN
  USER
  CUSTOMER
  CLIENT
}

enum LanguageCode {
  EN
  AR
  FR
  // Add any other languages you might support in the future
}

model Translation {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  refId        String // ID of the record being translated (String to accommodate int/uuid)
  tableName    String // Name of the table (e.g., "Service", "Queue")
  refField     String // Name of the field (e.g., "title", "description")
  languageCode LanguageCode // The language of this 'text'
  text         String       @db.Text // The translated (or original) text

  @@unique([tableName, refId, refField, languageCode], name: "unique_translation_entry")
  @@index([tableName, refId, refField])
  @@index([languageCode])
}

model User {
  id           String   @id @default(uuid())
  createdAt    DateTime @default(now())
  email        String?  @unique
  username     String?  @unique
  isA<PERSON><PERSON>  @default(false)
  // NOTE: Based on the current schema definition (role Role), a user can only have ONE role.
  // To allow multiple roles, this field type would need to be changed, for example to `Role[]` (if the database supports enum arrays)
  // or by introducing a separate relation table (e.g., UserRoles).
  role         Role     @default(CLIENT)
  firstName    String?
  lastName     String?
  mobileNumber String?  @unique
  nationalId   String?  @unique

  paymentProcessorUserId        String?   @unique
  lemonSqueezyCustomerPortalUrl String? // You can delete this if you're not using Lemon Squeezy as your payments processor.
  subscriptionStatus            String? // 'active', 'cancel_at_period_end', 'past_due', 'deleted'
  subscriptionPlan              String? // 'hobby', 'pro'
  datePaid                      DateTime?
  credits                       Int       @default(3)
  queues                        Int       @default(1)

  gptResponses        GptResponse[]
  contactFormMessages ContactFormMessage[]
  tasks               Task[]
  files               File[]

  preferedLanguage LanguageCode?

  // Profile picture relation
  profilePictureId String?
  profilePicture   File?   @relation("UserProfilePicture", fields: [profilePictureId], references: [id], onDelete: SetNull)

  // passwordResetOtp           String?
  // passwordResetOtpExpiresAt  DateTime?
  // lastPasswordResetOtpSentAt DateTime? // For cooldown

  // Relations for appointment system
  appointmentHistory AppointmentHistory[] // History changed by this user
  serviceProvider    SProvider? // If the user is a service provider
  customerFolders    CustomerFolder[] // Relation to folders where this user is the customer

  // Reschedule requests relations
  requestedReschedules RescheduleRequest[] @relation("RequestedByUser")
  respondedReschedules RescheduleRequest[] @relation("RespondedByUser")

  // Queue Swap Requests initiated by this user
  initiatedQueueSwapRequests QueueSwapRequest[]

  // --- OTP and Phone Verification Fields ---
  phoneOtp           String?
  phoneOtpExpiresAt  DateTime?
  isPhoneVerified    Boolean   @default(false)
  lastOtpSentAt      DateTime?
  // --- End OTP Fields ---
  emailOtp           String?
  emailOtpExpiresAt  DateTime?
  lastEmailOtpSentAt DateTime?
  isEmailVerified    Boolean   @default(false)

  // Password reset fields
  passwordResetToken          String?
  passwordResetTokenExpiresAt DateTime?

  deviceTokens UserDeviceToken[]
  addresses    Address[] // User can have multiple addresses

  notifications          Notification[] @relation("UserNotifications") // Notifications received by this user
  triggeredNotifications Notification[] @relation("ActorNotifications") // Notifications triggered by this user

  // --- Chat Relations ---
  ownedConversations          Conversation[]  @relation("OwnerOfConversation")
  participatedInConversations Participant[]   @relation("UserParticipations")
  sentMessages                Message[]       @relation("SentMessages")
  messageStatuses             MessageStatus[] @relation("MessageStatuses")
  // --- End Chat Relations ---

  reviewsWritten Review[] @relation("CustomerReviews")

  // This unique constraint includes the single 'role'. If multiple roles are implemented, this constraint would need reconsideration.
  @@unique([mobileNumber, nationalId, role])
  @@unique([profilePictureId])
}

model UserDeviceToken {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation(fields: [userId], references: [id], onDelete: Cascade)

  token      String @unique // The FCM registration token
  deviceType String // e.g., "web", "android", "ios"

  @@index([userId])
}

model GptResponse {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user   User   @relation(fields: [userId], references: [id])
  userId String

  content String
}

model Task {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id])
  userId String

  description String
  time        String  @default("1")
  isDone      Boolean @default(false)
}

model File {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id])
  userId String

  name      String
  type      String
  key       String
  uploadUrl String

  // Relations for profile pictures, provider logos, category images, and advertisements
  userProfilePicture User?             @relation("UserProfilePicture")
  providerLogo       SProvider?        @relation("ProviderLogo")
  categoryImage      ProviderCategory? @relation("CategoryImage")
  advertisementBackgroundImage Advertisement? @relation("AdvertisementBackgroundImage")
  advertisementPngImage Advertisement? @relation("AdvertisementPngImage")
}

model DailyStats {
  id   Int      @id @default(autoincrement())
  date DateTime @unique @default(now())

  totalViews                Int    @default(0)
  prevDayViewsChangePercent String @default("0")
  userCount                 Int    @default(0)
  paidUserCount             Int    @default(0)
  userDelta                 Int    @default(0)
  paidUserDelta             Int    @default(0)
  totalRevenue              Float  @default(0)
  totalProfit               Float  @default(0)

  sources PageViewSource[]
}

model PageViewSource {
  name String
  date DateTime @default(now())

  dailyStats   DailyStats? @relation(fields: [dailyStatsId], references: [id])
  dailyStatsId Int?

  visitors Int

  @@id([date, name])
}

model Logs {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())

  message String
  level   String
}

model ContactFormMessage {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())

  user   User   @relation(fields: [userId], references: [id])
  userId String

  content   String
  isRead    Boolean   @default(false)
  repliedAt DateTime?
}

// NEW MODEL: Placed after User
model CustomerFolder {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  customer User   @relation(fields: [userId], references: [id])
  userId   String

  notes    String? @db.Text
  isActive Boolean @default(true)

  appointments Appointment[]

  // --- Chat Relations ---
  // ownedConversations          Conversation[]          @relation("OwnerOfConversation") // REMOVED FROM HERE
  // participatedInConversations Participant[]           @relation("UserParticipations") // REMOVED FROM HERE
  // sentMessages                Message[]               @relation("SentMessages") // REMOVED FROM HERE
  // messageStatuses             MessageStatus[]         @relation("MessageStatuses") // REMOVED FROM HERE
  // --- End Chat Relations ---

  @@unique([sProviderId, userId])
}

// NEW MODEL: Address - for storing structured address information with geolocation
model Address {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  address    String
  city       String
  state      String? // e.g., CA, NY. Optional as not all countries use states.
  postalCode String
  country    String  @default("Algeria")

  latitude  Float // For geolocation
  longitude Float // For geolocation

  description String? // e.g., "Home", "Work", "Clinic Main Entrance"
  isPrimary   Boolean @default(false) // Useful if an entity (like User) can have multiple addresses

  // Relation to User (a user can have multiple addresses)
  // onDelete: Cascade means if the User is deleted, their associated Addresses are also deleted.
  user   User?   @relation(fields: [userId], references: [id], onDelete: Cascade)
  userId String?

  // Back-relation for SProvidingPlace (an SProvidingPlace can have one detailed Address)
  // The SProvidingPlace model will hold the foreign key 'detailedAddressId'.
  sProvidingPlace SProvidingPlace?
  // For advanced geospatial queries (e.g., nearest places) with PostgreSQL,
  // consider enabling the PostGIS extension and creating a spatial index (e.g., USING GIST)
  // on a geography/geometry column created from latitude and longitude via a raw migration.

  @@index([userId])
  @@index([latitude, longitude]) // Standard composite index for geolocation fields.
}

// --- Appointment Management Models ---

model Appointment {
  id                           Int                  @id @default(autoincrement())
  createdAt                    DateTime             @default(now())
  updatedAt                    DateTime             @updatedAt
  canceledAt                   DateTime?
  customerFolder               CustomerFolder       @relation(fields: [customerFolderId], references: [id])
  customerFolderId             Int
  typeEvent                    String               @default("clinic")
  status                       String               @default("pending") // e.g. pending, confirmed, canceled, completed, noshow, InProgress
  place                        SProvidingPlace      @relation(fields: [placeId], references: [id])
  placeId                      Int
  service                      Service              @relation(fields: [serviceId], references: [id])
  serviceId                    Int
  serviceDuration              Int
  expectedAppointmentStartTime DateTime?
  expectedAppointmentEndTime   DateTime?
  realAppointmentStartTime     DateTime? // Actual start time when session begins
  realAppointmentEndTime       DateTime? // Actual end time when session completes
  notes                        String?              @db.Text
  openingHours                 OpeningHours?        @relation(fields: [openingHoursId], references: [id])
  openingHoursId               Int?
  queue                        Queue?               @relation(fields: [queueId], references: [id])
  queueId                      Int? // Nullable to support existing appointments
  realTimeStatus               String               @default("ontime")
  slots                        Int                  @default(1)
  history                      AppointmentHistory[]
  isOverflowed                 Boolean?             @default(false)
  overflowReason               String? // e.g., 'shifted_beyond_hours', 'provider_declared_early_closure'
  overflowProcessingStatus     String? // e.g., 'pending', 'confirmed_overtime', 'reschedule_requested', 'canceled', 'completed'
  overflowDetectedAt           DateTime? // When the overflow was first detected
  overflowProcessedAt          DateTime?
  rescheduleRequests           RescheduleRequest[]
  initiatedSwapRequests        QueueSwapRequest[]   @relation("SwapAppointment1")
  involvedSwapRequests         QueueSwapRequest[]   @relation("SwapAppointment2")
  review                       Review?
}

model AppointmentHistory {
  id                Int         @id @default(autoincrement())
  appointment       Appointment @relation(fields: [appointmentId], references: [id])
  appointmentId     Int
  changedByUser     User        @relation(fields: [changedByUserId], references: [id])
  changedByUserId   String
  previousStartTime DateTime
  previousEndTime   DateTime
  newStartTime      DateTime
  newEndTime        DateTime
  previousStatus    String
  newStatus         String
  changeReason      String // e.g. 'Customer cancellation', 'Provider reschedule', 'Automatic time shift'
  previousMotifId   Int // Using previousMotifId for previousServiceId
  newMotifId        Int // Using newMotifId for newServiceId
  previousAgendaId  Int // Using previousAgendaId for previousQueueId
  newAgendaId       Int // Using newAgendaId for newQueueId
  createdAt         DateTime    @default(now())
}

// --- NEW: Queue Management Models ---

// Represents a specific bookable resource within a providing place (e.g., Chair 1, Room B)
model Queue {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  title String

  sProvidingPlace   SProvidingPlace @relation(fields: [sProvidingPlaceId], references: [id])
  sProvidingPlaceId Int

  isActive Boolean @default(true)

  // Queue-specific opening schedule
  openings     QueueOpening[]
  // Services offered by this specific queue
  services     Service[]      @relation("ServiceQueues")
  // Appointments booked for this specific queue
  appointments Appointment[]
  SProvider    SProvider?     @relation(fields: [sProviderId], references: [id])
  sProviderId  Int?

  // A queue name should be unique within its place
  @@unique([title, sProvidingPlaceId])
}

// Represents the opening schedule definition for a specific day of the week for a Queue
model QueueOpening {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  queue   Queue @relation(fields: [queueId], references: [id])
  queueId Int

  dayOfWeek String // e.g., "Monday", "Tuesday"
  type      String  @default("regular") // e.g., "regular", "exception"
  isActive  Boolean @default(true)

  // Time intervals for this opening
  hours QueueOpeningHours[]

  // An opening for a queue on a specific day/type should be unique
  @@unique([queueId, dayOfWeek, type])
}

// Represents a specific time interval (e.g., 09:00 - 12:00) within a QueueOpening
model QueueOpeningHours {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  queueOpening   QueueOpening @relation(fields: [queueOpeningId], references: [id])
  queueOpeningId Int

  timeFrom String // HH:mm format
  timeTo   String // HH:mm format
}

// --- END: Queue Management Models ---

model SProvider {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  category           ProviderCategory? @relation(fields: [providerCategoryId], references: [id])
  providerCategoryId Int?

  title           String?
  phone           String?
  presentation    String? @db.Text
  isVerified      Boolean @default(false)
  isSetupComplete Boolean @default(false) // Track if provider has completed onboarding setup

  // Logo relation
  logoId String?
  logo   File?   @relation("ProviderLogo", fields: [logoId], references: [id], onDelete: SetNull)

  user   User   @relation(fields: [userId], references: [id])
  userId String @unique

  providingPlaces             SProvidingPlace[]
  services                    Service[]
  serviceCategories           ServiceCategory[]
  customerFolders             CustomerFolder[]
  queues                      Queue[]
  isLowPriorityInSearchResult Boolean           @default(false)

  reviewsReceived Review[] @relation("ProviderReviews")
  averageRating   Float?
  totalReviews    Int      @default(0)

  @@unique([logoId])
}

model ProviderCategory {
  id          Int      @id @default(autoincrement())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @default(now()) @updatedAt
  title       String   @unique
  description String?  @db.Text
  isActive    Boolean  @default(true)
  sortOrder   Int      @default(0)
  metadata    Json?    // Stores icon, color, keywords, seoTitle, seoDescription

  parentId Int?
  parent   ProviderCategory?  @relation("CategoryHierarchy", fields: [parentId], references: [id])
  children ProviderCategory[] @relation("CategoryHierarchy")

  // Category image relation
  imageId String?
  image   File?   @relation("CategoryImage", fields: [imageId], references: [id], onDelete: SetNull)

  providers SProvider[]

  @@unique([imageId])
  @@index([isActive])
  @@index([sortOrder])
  @@index([parentId])
}

model SProvidingPlace {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  name           String
  shortName      String?
  address        String?
  city           String?
  mobile         String?
  isMobileHidden Boolean @default(false)
  fax            String?
  floor          String?
  parking        Boolean @default(false)
  elevator       Boolean @default(false)
  handicapAccess Boolean @default(false)
  timezone       String?

  appointments Appointment[]
  openings     Opening[]
  queues       Queue[]

  // New structured Address relation (optional to allow phased migration)
  // Renamed to 'detailedAddress' to avoid conflict with the existing 'address: String?' field.
  // If the linked Address is deleted, detailedAddressId on SProvidingPlace will be set to null.
  detailedAddress   Address? @relation(fields: [detailedAddressId], references: [id], onDelete: SetNull)
  detailedAddressId Int?     @unique
}

model Service {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  title String
  color String?

  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  category          ServiceCategory? @relation(fields: [serviceCategoryId], references: [id])
  serviceCategoryId Int?

  duration    Int
  minDuration Int?
  maxDuration Int?
  queue       Int?

  acceptOnline       Boolean @default(true)
  acceptNew          Boolean @default(true)
  notificationOn     Boolean @default(true)
  pointsRequirements Int     @default(1)

  // Pricing and delivery options for onboarding setup
  price         Float? // Optional price for the service
  isPublic      Boolean       @default(true) // Visibility: public/private booking
  deliveryType  String? // "at_location", "at_customer", "both"
  servedRegions String?       @db.Text // JSON array of wilaya/region IDs for "at_customer" delivery
  description   String?       @db.Text // Description of the service
  appointments  Appointment[]
  queues        Queue[]       @relation("ServiceQueues")
}

model ServiceCategory {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  title String

  provider    SProvider @relation(fields: [sProviderId], references: [id])
  sProviderId Int

  services Service[]

  @@unique([title, sProviderId])
}

model Opening {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  place             SProvidingPlace @relation(fields: [sProvidingPlaceId], references: [id])
  sProvidingPlaceId Int

  dayOfWeek String
  type      String  @default("regular")
  isActive  Boolean @default(true)

  hours OpeningHours[]

  @@unique([sProvidingPlaceId, dayOfWeek, type])
}

model OpeningHours {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  opening   Opening @relation(fields: [openingId], references: [id])
  openingId Int

  timeFrom String
  timeTo   String

  appointments Appointment[]
}

// New model for rescheduling requests
model RescheduleRequest {
  id                 Int         @id @default(autoincrement())
  appointment        Appointment @relation(fields: [appointmentId], references: [id])
  appointmentId      Int
  requestedBy        User        @relation("RequestedByUser", fields: [requestedById], references: [id])
  requestedById      String
  respondedBy        User?       @relation("RespondedByUser", fields: [respondedById], references: [id]) // Can be null if not yet responded
  respondedById      String?
  suggestedStartTime DateTime
  suggestedEndTime   DateTime
  status             String      @default("pending") // e.g. pending, accepted, rejected, expired, canceled
  responseTime       DateTime?
  reason             String? // Reason for requesting reschedule
  responseNote       String? // Note from the responder (e.g., why rejected)
  expiresAt          DateTime // When this request expires
  createdAt          DateTime    @default(now())
  updatedAt          DateTime    @updatedAt

  @@index([appointmentId])
  @@index([requestedById])
  @@index([respondedById])
}

model QueueSwapRequest {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  appointment1   Appointment @relation("SwapAppointment1", fields: [appointment1Id], references: [id])
  appointment1Id Int

  appointment2   Appointment @relation("SwapAppointment2", fields: [appointment2Id], references: [id])
  appointment2Id Int

  requestedBy   User   @relation(fields: [requestedById], references: [id])
  requestedById String

  status String @default("pending_customer2_approval")

  expiresAt DateTime?

  notes String?

  @@index([appointment1Id])
  @@index([appointment2Id])
  @@index([requestedById])
  @@index([status])
}

// --- Notification Model ---
model Notification {
  id        String   @id @default(uuid())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  userId String
  user   User   @relation("UserNotifications", fields: [userId], references: [id], onDelete: Cascade)

  type    String // e.g., "NEW_APPOINTMENT", "APPOINTMENT_REMINDER", "STATUS_UPDATE", "MESSAGE_RECEIVED"
  title   String
  message String    @db.Text
  isRead  Boolean   @default(false)
  readAt  DateTime?
  link    String? // Optional URL for navigation

  actorId String?
  actor   User?   @relation("ActorNotifications", fields: [actorId], references: [id], onDelete: SetNull) // User who triggered the notification

  @@index([userId, createdAt])
  @@index([userId, isRead])
}

enum MessageReadStatus {
  SENT
  DELIVERED
  READ
}

model Conversation {
  id          Int      @id @default(autoincrement())
  name        String?
  isGroup     Boolean  @default(false)
  ownerDomain String   @default("user")
  ownerId     String // User.id (UUID)
  owner       User     @relation("OwnerOfConversation", fields: [ownerId], references: [id], onDelete: Cascade)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  participants Participant[]
  messages     Message[]

  lastMessageId Int?     @unique
  lastMessage   Message? @relation("LastMessageInConversation", fields: [lastMessageId], references: [id], onDelete: SetNull, onUpdate: NoAction)

  @@index([ownerId])
  @@index([updatedAt])
}

model Participant {
  id             Int          @id @default(autoincrement())
  conversationId Int
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  userId         String // User.id (UUID)
  user           User         @relation("UserParticipations", fields: [userId], references: [id], onDelete: Cascade)
  userDomain     String       @default("user")
  canWrite       Boolean      @default(true)
  canDownload    Boolean      @default(true) // For file attachments in the future
  unreadCount    Int          @default(0)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  @@unique([conversationId, userId])
  @@index([createdAt])
}

model Message {
  id             Int          @id @default(autoincrement())
  conversationId Int
  conversation   Conversation @relation(fields: [conversationId], references: [id], onDelete: Cascade)
  senderId       String // User.id (UUID)
  sender         User         @relation("SentMessages", fields: [senderId], references: [id], onDelete: Cascade)
  senderDomain   String       @default("user")
  content        String       @db.Text
  hasAttachments Boolean      @default(false)
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt

  statuses                MessageStatus[]
  // Relation back to Conversation if this message is the last one
  conversationLastMessage Conversation?   @relation("LastMessageInConversation")

  @@index([senderId])
  @@index([createdAt])
}

model MessageStatus {
  id        Int               @id @default(autoincrement())
  messageId Int
  message   Message           @relation(fields: [messageId], references: [id], onDelete: Cascade)
  userId    String // User.id (UUID)
  user      User              @relation("MessageStatuses", fields: [userId], references: [id], onDelete: Cascade)
  status    MessageReadStatus
  timestamp DateTime          @default(now()) // When this status was recorded
  updatedAt DateTime          @updatedAt // If status can change and we want to track that

  @@unique([messageId, userId])
}

// Review model for provider reviews
model Review {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  rating  Int // 1-5 stars
  comment String? @db.Text
  status  String  @default("approved") // pending_approval, approved, rejected

  // Customer who wrote the review
  customer   User   @relation("CustomerReviews", fields: [customerId], references: [id])
  customerId String

  // Provider being reviewed
  provider   SProvider @relation("ProviderReviews", fields: [providerId], references: [id])
  providerId Int

  // Optional link to specific appointment
  appointment   Appointment? @relation(fields: [appointmentId], references: [id])
  appointmentId Int?         @unique // One review per appointment

  // Provider's response to the review
  response ReviewResponse?

  @@index([customerId])
  @@index([providerId])
  @@index([appointmentId])
}

// Model for provider responses to reviews
model ReviewResponse {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  content String @db.Text

  review   Review @relation(fields: [reviewId], references: [id], onDelete: Cascade)
  reviewId Int    @unique // One response per review

  @@index([reviewId])
}

model Advertisement {
  id        Int      @id @default(autoincrement())
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  title              String
  subtitle           String?
  description        String? @db.Text
  callToActionText   String
  callToActionLink   String
  isExternal         Boolean @default(false)
  isActive           Boolean @default(true)
  sortOrder          Int     @default(0)

  // Background image relation (optional)
  backgroundImageId String?
  backgroundImage   File?   @relation("AdvertisementBackgroundImage", fields: [backgroundImageId], references: [id], onDelete: SetNull)

  // PNG image relation (optional)
  pngImageId String?
  pngImage   File?   @relation("AdvertisementPngImage", fields: [pngImageId], references: [id], onDelete: SetNull)

  @@unique([backgroundImageId])
  @@unique([pngImageId])
  @@index([isActive])
  @@index([sortOrder])
}
