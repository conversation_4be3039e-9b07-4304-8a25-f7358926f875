import { HttpError } from 'wasp/server';
import { searchProviders , getProviderCategories, getProviderAvailability, createCustomerAppointment, getUserServiceProvider, extendAppointment } from 'wasp/server/operations';
import { z } from 'zod';
import type { Request, Response } from 'express';
import { type ProviderCategory, SProvider, SProvidingPlace, Service, Queue } from 'wasp/entities';
import { type DailyAvailabilityResult } from './operations';
import { calculateProfileCompletion, type ProfileCompletionResult } from './utils/profileCompletion';

// This schema matches the input for the searchProviders Wasp ACTION
const searchProvidersApiInputSchema = z.object({
  categoryId: z.coerce.number().int().optional(),
  q: z.string().optional(),
  city: z.string().optional(),
  skip: z.coerce.number().int().nonnegative().optional(),
  take: z.coerce.number().int().positive().optional(),
  targetLanguage: z.string().optional(),
});

export const handleSearchProviders = async (req: Request, res: Response, context: any) => {
  try {
    const validatedQuery = searchProvidersApiInputSchema.safeParse(req.query);

    if (!validatedQuery.success) {
      return res.status(400).json({ message: "Invalid query parameters", errors: validatedQuery.error.format() });
    }

    const args = validatedQuery.data;

    const results = await searchProviders(args, context);

    res.status(200).json(results);
  } catch (error: any) {
    console.error("Error in handleSearchProviders API:", error);
    if (error instanceof HttpError) {
      return res.status(error.statusCode).json({ message: error.message });
    }
    return res.status(500).json({ message: error.message || 'Failed to search providers via API.' });
  }
};

// Handler for getProviderCategories
export const handleGetProviderCategories = async (req: Request, res: Response, context: any) => {
  try {
    // getProviderCategories action does not take arguments
    const categories = await getProviderCategories(req.query, context as any);

    res.status(200).json(categories);
  } catch (error: any) {
    console.error("Error in handleGetProviderCategories API:", error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    res.status(statusCode).json({ message: error.message || 'Failed to fetch provider categories' });
  }
};

// --- API Handler for getProviderAvailability ---

const getProviderAvailabilityParamsSchema = z.object({
  sProvidingPlaceId: z.string().regex(/^\d+$/, "sProvidingPlaceId must be a number").transform(Number),
  serviceId: z.string().regex(/^\d+$/, "serviceId must be a number").transform(Number),
  startDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "startDate must be in YYYY-MM-DD format"),
  endDate: z.string().regex(/^\d{4}-\d{2}-\d{2}$/, "endDate must be in YYYY-MM-DD format"),
  queueId: z.string().regex(/^\d+$/, "queueId must be a number").transform(Number).optional(),
});

export const handleGetProviderAvailability = async (req: Request, res: Response, context: any) => {
  try {
    const parsedQuery = getProviderAvailabilityParamsSchema.safeParse(req.query);
    if (!parsedQuery.success) {
      // Use ZodError's issues to provide a more detailed message
      const errorDetails = parsedQuery.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      throw new HttpError(400, 'Invalid query parameters: ' + errorDetails);
    }

    const args = parsedQuery.data;

    const availabilityResults: DailyAvailabilityResult[] = await getProviderAvailability(args, context);
    res.json(availabilityResults);

  } catch (error: any) {
    console.error("[API] Failed to get provider availability:", error);
    const statusCode = error instanceof HttpError ? error.statusCode :
                   (error.name === 'ZodError' ? 400 : 500);
    const message = error instanceof HttpError ? error.message :
                   (error.name === 'ZodError' ? 'Invalid input: ' + JSON.stringify(error.errors) :
                   'Failed to fetch provider availability');
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for getProviderAvailability ---

// --- API Handler for createCustomerAppointment ---

// This schema should align with createCustomerAppointmentInputSchema in provider/operations.ts
// but receive dates as strings initially, then coerce them.
const createCustomerAppointmentApiSchema = z.object({
  placeId: z.number().int().positive(),
  serviceId: z.number().int().positive(),
  queueId: z.number().int().positive(),
  startTime: z.string().datetime({ message: "startTime must be a valid ISO 8601 date-time string" }).transform(val => new Date(val)),
  endTime: z.string().datetime({ message: "endTime must be a valid ISO 8601 date-time string" }).transform(val => new Date(val)),
  // notes: z.string().optional().nullable(), // If notes were part of the action
}).refine(data => data.endTime > data.startTime, {
  message: "End time must be after start time",
  path: ["endTime"],
});

export const handleCreateCustomerAppointment = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    // This should ideally be caught by Wasp's auth: true on the API route,
    // but an explicit check is good practice.
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    const parsedBody = createCustomerAppointmentApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      const errorDetails = parsedBody.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      throw new HttpError(400, 'Invalid request body: ' + errorDetails);
    }

    const args = parsedBody.data;

    // Ensure context.user is passed if the action relies on it directly
    // The createCustomerAppointment action in Wasp should automatically have access to context.user
    const newAppointment = await createCustomerAppointment(args, context);

    res.status(201).json(newAppointment);

  } catch (error: any) {
    console.error("[API] Failed to create customer appointment:", error);
    const statusCode = error instanceof HttpError ? error.statusCode :
                   (error.name === 'ZodError' ? 400 : 500);
    const message = error instanceof HttpError ? error.message :
                   (error.name === 'ZodError' ? 'Invalid input: ' + JSON.stringify(error.errors) :
                   'Failed to create customer appointment');
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for createCustomerAppointment --- 

// --- API Handler for getProviderProfileCompletion ---

export const handleGetProviderProfileCompletion = async (req: Request, res: Response, context: any) => {
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    // Fetch the provider data with all necessary includes
    let provider = await getUserServiceProvider({ userId: context.user.id }, context);

    if (!provider) {
      throw new HttpError(404, 'Provider profile not found');
    }

    // Calculate profile completion using the existing utility
    const completionResult: ProfileCompletionResult = calculateProfileCompletion({
      user: context.user,
      provider: provider,
    });

    // If completion is 100% and not yet marked, update the provider
    if (completionResult.shouldMarkAsComplete) {
      // Re-fetch the provider with all includes after update
      provider = await context.entities.SProvider.update({
        where: { id: provider.id },
        data: { isSetupComplete: true },
        include: {
          category: true,
          providingPlaces: {
            include: {
              detailedAddress: true,
              queues: true,
            },
          },
          services: true,
          logo: true, // ensure logo is included as it is part of completion
        },
      });

      // Recalculate with the updated provider to ensure the response is consistent
      const newCompletionResult = calculateProfileCompletion({
        user: context.user,
        provider: provider,
      });

      return res.status(200).json({
        success: true,
        data: newCompletionResult,
        message: 'Profile completion calculated and status updated successfully.',
      });
    }


    res.status(200).json({
      success: true,
      data: completionResult,
      message: 'Profile completion calculated successfully',
    });
  } catch (error: any) {
    console.error('[API] Failed to calculate profile completion:', error);
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to calculate profile completion';
    res.status(statusCode).json({
      success: false,
      message,
      error: process.env.NODE_ENV === 'development' ? error.stack : undefined,
    });
  }
};

// --- END API Handler for getProviderProfileCompletion ---

// --- API Handler for extendAppointment ---

const extendAppointmentApiSchema = z.object({
  appointmentId: z.number().int().positive(),
  extensionMinutes: z.number().int().positive().max(60, "Extension cannot exceed 60 minutes"),
});

export const handleExtendAppointment = async (req: Request, res: Response, context: any) => {
  console.log("[API] handleExtendAppointment called with:", req.body);
  console.log("[API] User context:", context.user?.id);
  
  if (!context.user) {
    return res.status(401).json({ message: "User not authenticated." });
  }

  try {
    const parsedBody = extendAppointmentApiSchema.safeParse(req.body);
    if (!parsedBody.success) {
      const errorDetails = parsedBody.error.issues.map(issue => `${issue.path.join('.')} - ${issue.message}`).join('; ');
      console.error("[API] Validation failed:", errorDetails);
      throw new HttpError(400, 'Invalid request body: ' + errorDetails);
    }

    const args = parsedBody.data;
    console.log("[API] Validated args:", args);

    // Call the extendAppointment operation
    const extendedAppointment = await extendAppointment(args, context);

    console.log("[API] Successfully extended appointment");
    res.status(200).json(extendedAppointment);

  } catch (error: any) {
    console.error("[API] Failed to extend appointment:", error);
    console.error("[API] Error details:", {
      name: error.name,
      message: error.message,
      statusCode: error.statusCode,
      stack: error.stack
    });
    
    const statusCode = error instanceof HttpError ? error.statusCode : 500;
    const message = error instanceof HttpError ? error.message : 'Failed to extend appointment';
    
    res.status(statusCode).json({ message });
  }
};

// --- END API Handler for extendAppointment --- 